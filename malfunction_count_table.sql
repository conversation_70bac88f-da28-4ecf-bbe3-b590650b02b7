-- 创建原始数据表（存储每条故障记录）
CREATE TABLE malfunction_raw_data (
    id INT AUTO_INCREMENT PRIMARY KEY,
    line INT NOT NULL COMMENT '生产线编号',
    status CHAR(1) NOT NULL COMMENT '故障状态类型 A-J',
    cmd TINYINT NOT NULL COMMENT '操作命令：1=增加，0=减少',
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录时间',
    processed BOOLEAN DEFAULT FALSE COMMENT '是否已处理到统计表',
    INDEX idx_line (line),
    INDEX idx_status (status),
    INDEX idx_timestamp (timestamp),
    INDEX idx_processed (processed)
);

-- 创建坏机类型每日计数表
CREATE TABLE malfunction_daily_count (
    id INT AUTO_INCREMENT PRIMARY KEY,
    malfunction_type CHAR(1) NOT NULL COMMENT '坏机类型 A-J',
    count INT NOT NULL DEFAULT 0 COMMENT '坏机次数',
    date_timestamp DATE NOT NULL COMMENT '统计日期',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_type_date (malfunction_type, date_timestamp)
);

-- 创建坏机类型每小时计数表
CREATE TABLE malfunction_hourly_count (
    id INT AUTO_INCREMENT PRIMARY KEY,
    malfunction_type CHAR(1) NOT NULL COMMENT '坏机类型 A-J',
    count INT NOT NULL DEFAULT 0 COMMENT '坏机次数',
    hour_timestamp DATETIME NOT NULL COMMENT '小时时间戳 (YYYY-MM-DD HH:00:00)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_type_hour (malfunction_type, hour_timestamp)
);

-- 创建坏机类型说明表（可选）
CREATE TABLE malfunction_type_info (
    type_code CHAR(1) PRIMARY KEY COMMENT '坏机类型代码 A-J',
    type_name VARCHAR(50) COMMENT '坏机类型名称',
    description TEXT COMMENT '类型描述'
);

-- 插入坏机类型说明
INSERT INTO malfunction_type_info (type_code, type_name, description) VALUES
('A', '机械故障', '机械部件损坏或磨损'),
('B', '电气故障', '电路或电气元件故障'),
('C', '传感器故障', '传感器失效或读数异常'),
('D', '软件故障', '程序错误或系统异常'),
('E', '温度异常', '设备过热或温度控制失效'),
('F', '压力异常', '压力系统故障'),
('G', '材料卡顿', '原材料供给问题'),
('H', '清洁问题', '设备需要清洁维护'),
('I', '校准偏差', '设备需要重新校准'),
('J', '其他故障', '未分类的其他故障类型');

-- 初始化今日数据（所有类型初始为0）
INSERT INTO malfunction_daily_count (malfunction_type, count, date_timestamp) VALUES
('A', 0, CURDATE()),
('B', 0, CURDATE()),
('C', 0, CURDATE()),
('D', 0, CURDATE()),
('E', 0, CURDATE()),
('F', 0, CURDATE()),
('G', 0, CURDATE()),
('H', 0, CURDATE()),
('I', 0, CURDATE()),
('J', 0, CURDATE());

-- 初始化当前小时数据（所有类型初始为0）
INSERT INTO malfunction_hourly_count (malfunction_type, count, hour_timestamp) VALUES
('A', 0, DATE_FORMAT(NOW(), '%Y-%m-%d %H:00:00')),
('B', 0, DATE_FORMAT(NOW(), '%Y-%m-%d %H:00:00')),
('C', 0, DATE_FORMAT(NOW(), '%Y-%m-%d %H:00:00')),
('D', 0, DATE_FORMAT(NOW(), '%Y-%m-%d %H:00:00')),
('E', 0, DATE_FORMAT(NOW(), '%Y-%m-%d %H:00:00')),
('F', 0, DATE_FORMAT(NOW(), '%Y-%m-%d %H:00:00')),
('G', 0, DATE_FORMAT(NOW(), '%Y-%m-%d %H:00:00')),
('H', 0, DATE_FORMAT(NOW(), '%Y-%m-%d %H:00:00')),
('I', 0, DATE_FORMAT(NOW(), '%Y-%m-%d %H:00:00')),
('J', 0, DATE_FORMAT(NOW(), '%Y-%m-%d %H:00:00'));

-- 创建存储过程：插入原始数据并处理
DELIMITER //
CREATE PROCEDURE insert_malfunction_raw_data(IN p_line INT, IN p_status CHAR(1), IN p_cmd TINYINT)
BEGIN
    DECLARE target_date DATE;
    DECLARE target_hour DATETIME;
    DECLARE count_change INT;
    
    -- 插入原始数据
    INSERT INTO malfunction_raw_data (line, status, cmd) 
    VALUES (p_line, p_status, p_cmd);
    
    -- 检查状态类型是否有效（A-J）
    IF p_status REGEXP '^[A-J]$' THEN
        SET target_date = CURDATE();
        SET target_hour = DATE_FORMAT(NOW(), '%Y-%m-%d %H:00:00');
        SET count_change = IF(p_cmd = 1, 1, -1);
        
        -- 更新每日计数
        INSERT INTO malfunction_daily_count (malfunction_type, count, date_timestamp)
        VALUES (p_status, GREATEST(0, count_change), target_date)
        ON DUPLICATE KEY UPDATE 
            count = GREATEST(0, count + count_change),
            updated_at = CURRENT_TIMESTAMP;
            
        -- 更新每小时计数
        INSERT INTO malfunction_hourly_count (malfunction_type, count, hour_timestamp)
        VALUES (p_status, GREATEST(0, count_change), target_hour)
        ON DUPLICATE KEY UPDATE 
            count = GREATEST(0, count + count_change),
            updated_at = CURRENT_TIMESTAMP;
    END IF;
END //
DELIMITER ;

-- 创建存储过程：批量处理未处理的原始数据
DELIMITER //
CREATE PROCEDURE process_raw_data()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_id INT;
    DECLARE v_line INT;
    DECLARE v_status CHAR(1);
    DECLARE v_cmd TINYINT;
    DECLARE v_timestamp TIMESTAMP;
    
    DECLARE cur CURSOR FOR 
        SELECT id, line, status, cmd, timestamp 
        FROM malfunction_raw_data 
        WHERE processed = FALSE 
        ORDER BY timestamp;
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    OPEN cur;
    
    read_loop: LOOP
        FETCH cur INTO v_id, v_line, v_status, v_cmd, v_timestamp;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- 处理这条记录
        CALL process_single_raw_record(v_status, v_cmd, v_timestamp);
        
        -- 标记为已处理
        UPDATE malfunction_raw_data SET processed = TRUE WHERE id = v_id;
    END LOOP;
    
    CLOSE cur;
END //
DELIMITER ;

-- 创建存储过程：处理单条原始记录
DELIMITER //
CREATE PROCEDURE process_single_raw_record(IN p_status CHAR(1), IN p_cmd TINYINT, IN p_timestamp TIMESTAMP)
BEGIN
    DECLARE target_date DATE;
    DECLARE target_hour DATETIME;
    DECLARE count_change INT;
    
    -- 检查状态类型是否有效（A-J）
    IF p_status REGEXP '^[A-J]$' THEN
        SET target_date = DATE(p_timestamp);
        SET target_hour = DATE_FORMAT(p_timestamp, '%Y-%m-%d %H:00:00');
        SET count_change = IF(p_cmd = 1, 1, -1);
        
        -- 更新每日计数
        INSERT INTO malfunction_daily_count (malfunction_type, count, date_timestamp)
        VALUES (p_status, GREATEST(0, count_change), target_date)
        ON DUPLICATE KEY UPDATE 
            count = GREATEST(0, count + count_change),
            updated_at = CURRENT_TIMESTAMP;
            
        -- 更新每小时计数
        INSERT INTO malfunction_hourly_count (malfunction_type, count, hour_timestamp)
        VALUES (p_status, GREATEST(0, count_change), target_hour)
        ON DUPLICATE KEY UPDATE 
            count = GREATEST(0, count + count_change),
            updated_at = CURRENT_TIMESTAMP;
    END IF;
END //
DELIMITER ;

-- 保留原有的增加坏机计数存储过程（兼容性）
DELIMITER //
CREATE PROCEDURE increment_malfunction_count(IN mal_type CHAR(1), IN mal_timestamp TIMESTAMP)
BEGIN
    DECLARE target_date DATE;
    DECLARE target_hour DATETIME;
    
    -- 检查类型是否有效（A-J）
    IF mal_type REGEXP '^[A-J]$' THEN
        SET target_date = DATE(mal_timestamp);
        SET target_hour = DATE_FORMAT(mal_timestamp, '%Y-%m-%d %H:00:00');
        
        -- 更新每日计数
        INSERT INTO malfunction_daily_count (malfunction_type, count, date_timestamp)
        VALUES (mal_type, 1, target_date)
        ON DUPLICATE KEY UPDATE 
            count = count + 1,
            updated_at = CURRENT_TIMESTAMP;
            
        -- 更新每小时计数
        INSERT INTO malfunction_hourly_count (malfunction_type, count, hour_timestamp)
        VALUES (mal_type, 1, target_hour)
        ON DUPLICATE KEY UPDATE 
            count = count + 1,
            updated_at = CURRENT_TIMESTAMP;
    END IF;
END //
DELIMITER ;

-- 创建存储过程：获取今日坏机统计
DELIMITER //
CREATE PROCEDURE get_today_malfunction_stats()
BEGIN
    SELECT 
        mdc.malfunction_type,
        mti.type_name,
        mdc.count
    FROM malfunction_daily_count mdc
    LEFT JOIN malfunction_type_info mti ON mdc.malfunction_type = mti.type_code
    WHERE mdc.date_timestamp = CURDATE()
    ORDER BY mdc.malfunction_type;
END //
DELIMITER ;

-- 创建存储过程：获取今日每小时坏机统计
DELIMITER //
CREATE PROCEDURE get_today_hourly_malfunction_stats()
BEGIN
    SELECT 
        mhc.malfunction_type,
        mti.type_name,
        mhc.hour_timestamp,
        HOUR(mhc.hour_timestamp) as hour,
        mhc.count
    FROM malfunction_hourly_count mhc
    LEFT JOIN malfunction_type_info mti ON mhc.malfunction_type = mti.type_code
    WHERE DATE(mhc.hour_timestamp) = CURDATE()
    ORDER BY mhc.hour_timestamp, mhc.malfunction_type;
END //
DELIMITER ;

-- 创建存储过程：获取指定小时的坏机统计
DELIMITER //
CREATE PROCEDURE get_hourly_malfunction_stats(IN target_hour DATETIME)
BEGIN
    SELECT 
        mti.type_code as malfunction_type,
        mti.type_name,
        COALESCE(mhc.count, 0) as count
    FROM malfunction_type_info mti
    LEFT JOIN malfunction_hourly_count mhc ON mti.type_code = mhc.malfunction_type 
        AND mhc.hour_timestamp = target_hour
    ORDER BY mti.type_code;
END //
DELIMITER ;

-- 插入一些原始数据测试（使用新的数据格式）
CALL insert_malfunction_raw_data(1, 'A', 1); -- 生产线1，故障类型A，增加
CALL insert_malfunction_raw_data(1, 'A', 1); -- 生产线1，故障类型A，增加
CALL insert_malfunction_raw_data(2, 'B', 1); -- 生产线2，故障类型B，增加
CALL insert_malfunction_raw_data(1, 'C', 1); -- 生产线1，故障类型C，增加
CALL insert_malfunction_raw_data(3, 'C', 1); -- 生产线3，故障类型C，增加
CALL insert_malfunction_raw_data(2, 'C', 1); -- 生产线2，故障类型C，增加
CALL insert_malfunction_raw_data(1, 'E', 1); -- 生产线1，故障类型E，增加
CALL insert_malfunction_raw_data(1, 'A', 0); -- 生产线1，故障类型A，减少

-- 创建存储过程：查询原始数据
DELIMITER //
CREATE PROCEDURE get_raw_data_by_line(IN p_line INT)
BEGIN
    SELECT 
        id,
        line,
        status,
        cmd,
        timestamp,
        processed,
        CASE cmd 
            WHEN 1 THEN '增加' 
            WHEN 0 THEN '减少' 
            ELSE '未知' 
        END as cmd_desc
    FROM malfunction_raw_data 
    WHERE line = p_line 
    ORDER BY timestamp DESC;
END //
DELIMITER ;

-- 创建存储过程：查询指定时间范围的原始数据
DELIMITER //
CREATE PROCEDURE get_raw_data_by_time_range(IN start_time TIMESTAMP, IN end_time TIMESTAMP)
BEGIN
    SELECT 
        mrd.id,
        mrd.line,
        mrd.status,
        mti.type_name,
        mrd.cmd,
        CASE mrd.cmd 
            WHEN 1 THEN '增加' 
            WHEN 0 THEN '减少' 
            ELSE '未知' 
        END as cmd_desc,
        mrd.timestamp,
        mrd.processed
    FROM malfunction_raw_data mrd
    LEFT JOIN malfunction_type_info mti ON mrd.status = mti.type_code
    WHERE mrd.timestamp BETWEEN start_time AND end_time 
    ORDER BY mrd.timestamp DESC;
END //
DELIMITER ;

-- 创建存储过程：获取今日原始数据统计
DELIMITER //
CREATE PROCEDURE get_today_raw_data_stats()
BEGIN
    SELECT 
        line,
        status,
        COUNT(*) as total_records,
        SUM(CASE WHEN cmd = 1 THEN 1 ELSE 0 END) as add_count,
        SUM(CASE WHEN cmd = 0 THEN 1 ELSE 0 END) as reduce_count,
        MAX(timestamp) as last_update
    FROM malfunction_raw_data 
    WHERE DATE(timestamp) = CURDATE()
    GROUP BY line, status
    ORDER BY line, status;
END //
DELIMITER ;

-- 插入一些测试数据（兼容旧格式）
CALL increment_malfunction_count('D', '2025-08-21 19:15:00');
CALL increment_malfunction_count('F', '2025-08-21 18:30:00');

-- 创建索引
CREATE INDEX idx_daily_malfunction_type ON malfunction_daily_count(malfunction_type);
CREATE INDEX idx_daily_malfunction_date ON malfunction_daily_count(date_timestamp);
CREATE INDEX idx_hourly_malfunction_type ON malfunction_hourly_count(malfunction_type);
CREATE INDEX idx_hourly_malfunction_hour ON malfunction_hourly_count(hour_timestamp);

-- 创建视图：今日坏机统计
CREATE VIEW view_today_malfunction AS
SELECT 
    mti.type_code as malfunction_type,
    mti.type_name,
    COALESCE(mdc.count, 0) as count
FROM malfunction_type_info mti
LEFT JOIN malfunction_daily_count mdc ON mti.type_code = mdc.malfunction_type 
    AND mdc.date_timestamp = CURDATE()
ORDER BY mti.type_code;

-- 创建视图：坏机排行榜（按次数降序）
CREATE VIEW view_malfunction_ranking AS
SELECT 
    mti.type_code as malfunction_type,
    mti.type_name,
    COALESCE(mdc.count, 0) as count
FROM malfunction_type_info mti
LEFT JOIN malfunction_daily_count mdc ON mti.type_code = mdc.malfunction_type 
    AND mdc.date_timestamp = CURDATE()
ORDER BY COALESCE(mdc.count, 0) DESC, mti.type_code;

-- 创建视图：今日每小时坏机趋势
CREATE VIEW view_today_hourly_malfunction AS
SELECT 
    mhc.hour_timestamp,
    HOUR(mhc.hour_timestamp) as hour,
    SUM(mhc.count) as total_count,
    GROUP_CONCAT(CONCAT(mhc.malfunction_type, ':', mhc.count) ORDER BY mhc.malfunction_type) as type_breakdown
FROM malfunction_hourly_count mhc
WHERE DATE(mhc.hour_timestamp) = CURDATE()
GROUP BY mhc.hour_timestamp
ORDER BY mhc.hour_timestamp;

-- 创建视图：当前小时各类型坏机统计
CREATE VIEW view_current_hour_malfunction AS
SELECT 
    mti.type_code as malfunction_type,
    mti.type_name,
    COALESCE(mhc.count, 0) as count
FROM malfunction_type_info mti
LEFT JOIN malfunction_hourly_count mhc ON mti.type_code = mhc.malfunction_type 
    AND mhc.hour_timestamp = DATE_FORMAT(NOW(), '%Y-%m-%d %H:00:00')
ORDER BY COALESCE(mhc.count, 0) DESC, mti.type_code;

-- 创建视图：原始数据详细信息
CREATE VIEW view_raw_data_detail AS
SELECT 
    mrd.id,
    mrd.line,
    mrd.status,
    mti.type_name,
    mrd.cmd,
    CASE mrd.cmd 
        WHEN 1 THEN '增加' 
        WHEN 0 THEN '减少' 
        ELSE '未知' 
    END as cmd_desc,
    mrd.timestamp,
    mrd.processed
FROM malfunction_raw_data mrd
LEFT JOIN malfunction_type_info mti ON mrd.status = mti.type_code
ORDER BY mrd.timestamp DESC;

-- 创建视图：今日原始数据汇总
CREATE VIEW view_today_raw_data_summary AS
SELECT 
    mrd.line,
    mrd.status,
    mti.type_name,
    COUNT(*) as total_records,
    SUM(CASE WHEN mrd.cmd = 1 THEN 1 ELSE 0 END) as add_operations,
    SUM(CASE WHEN mrd.cmd = 0 THEN 1 ELSE 0 END) as reduce_operations,
    MAX(mrd.timestamp) as last_operation_time
FROM malfunction_raw_data mrd
LEFT JOIN malfunction_type_info mti ON mrd.status = mti.type_code
WHERE DATE(mrd.timestamp) = CURDATE()
GROUP BY mrd.line, mrd.status, mti.type_name
ORDER BY mrd.line, mrd.status;

/*
使用示例：

1. 插入原始数据（按照你提供的格式）：
CALL insert_malfunction_raw_data(1, 'A', 1); -- 生产线1，故障A，增加

2. 查询今日统计：
CALL get_today_malfunction_stats();

3. 查询原始数据：
SELECT * FROM view_raw_data_detail WHERE line = 1;

4. 查询今日原始数据汇总：
SELECT * FROM view_today_raw_data_summary;

5. 查询指定生产线的原始数据：
CALL get_raw_data_by_line(1);

6. 批量处理未处理的原始数据：
CALL process_raw_data();

7. 查询指定时间范围的原始数据：
CALL get_raw_data_by_time_range('2025-08-21 00:00:00', '2025-08-21 23:59:59');

8. 查询今日原始数据统计：
CALL get_today_raw_data_stats();
*/
