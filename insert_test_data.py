#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
插入测试数据脚本
用于向数据库插入好机和坏机的测试数据，方便测试系统功能
"""

import mysql.connector
from mysql.connector import Error
import random
import time
from datetime import datetime, timedelta
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'database': 'production_data',
    'user': 'root',
    'password': '9f81cc9ba8f6458d',  # 请修改为实际密码
    'charset': 'utf8mb4',
    'autocommit': True
}

class TestDataInserter:
    def __init__(self):
        self.connection = None
        
    def connect_db(self):
        """连接数据库"""
        try:
            self.connection = mysql.connector.connect(**DB_CONFIG)
            if self.connection.is_connected():
                logging.info("数据库连接成功")
                return True
        except Error as e:
            logging.error(f"数据库连接失败: {e}")
            return False
        return False
    
    def close_db(self):
        """关闭数据库连接"""
        if self.connection and self.connection.is_connected():
            self.connection.close()
            logging.info("数据库连接已关闭")
    
    def insert_production_data(self, count=100):
        """插入生产线计数器测试数据"""
        try:
            if not self.connection or not self.connection.is_connected():
                if not self.connect_db():
                    return False
            
            cursor = self.connection.cursor()
            
            # 生成过去24小时的测试数据
            now = datetime.now()
            
            for i in range(count):
                # 随机时间（过去24小时内）
                random_minutes = random.randint(0, 1440)  # 24小时 = 1440分钟
                timestamp = now - timedelta(minutes=random_minutes)
                
                # 随机计数器值
                counter = random.randint(1, 999)
                
                # 插入数据
                sql = """
                INSERT INTO line1_message_log (topic, qos, counter, received_at)
                VALUES (%s, %s, %s, %s)
                """
                cursor.execute(sql, ('factory/line1/counter', 0, counter, timestamp))
            
            cursor.close()
            logging.info(f"成功插入 {count} 条生产线数据")
            return True
            
        except Error as e:
            logging.error(f"插入生产线数据失败: {e}")
            return False
    
    def insert_malfunction_data(self, count=50):
        """插入故障测试数据"""
        try:
            if not self.connection or not self.connection.is_connected():
                if not self.connect_db():
                    return False
            
            cursor = self.connection.cursor()
            
            # 故障类型
            malfunction_types = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J']
            
            # 生产线编号
            lines = [1, 2, 3, 4, 5]
            
            # 生成过去24小时的故障数据
            now = datetime.now()
            
            for i in range(count):
                # 随机时间（过去24小时内）
                random_minutes = random.randint(0, 1440)
                timestamp = now - timedelta(minutes=random_minutes)
                
                # 随机选择故障类型和生产线
                malfunction_type = random.choice(malfunction_types)
                line = random.choice(lines)
                
                # 随机命令（1表示故障发生，0表示故障恢复）
                # 70%概率是故障发生，30%概率是故障恢复
                cmd = 1 if random.random() < 0.7 else 0
                
                # 插入原始数据
                insert_sql = """
                INSERT INTO malfunction_raw_data (line, status, cmd, timestamp) 
                VALUES (%s, %s, %s, %s)
                """
                cursor.execute(insert_sql, (line, malfunction_type, cmd, timestamp))
                
                # 更新统计表
                self._update_statistics_with_time(cursor, malfunction_type, cmd, timestamp)
            
            cursor.close()
            logging.info(f"成功插入 {count} 条故障数据")
            return True
            
        except Error as e:
            logging.error(f"插入故障数据失败: {e}")
            return False
    
    def _update_statistics_with_time(self, cursor, status, cmd, timestamp):
        """根据指定时间更新统计表"""
        try:
            target_date = timestamp.date()
            target_hour = timestamp.replace(minute=0, second=0, microsecond=0)
            count_change = 1 if cmd == 1 else -1
            
            # 更新每日计数
            daily_sql = """
            INSERT INTO malfunction_daily_count (malfunction_type, count, date_timestamp)
            VALUES (%s, GREATEST(0, %s), %s)
            ON DUPLICATE KEY UPDATE 
                count = GREATEST(0, count + %s),
                updated_at = CURRENT_TIMESTAMP
            """
            cursor.execute(daily_sql, (status, count_change, target_date, count_change))
            
            # 更新每小时计数
            hourly_sql = """
            INSERT INTO malfunction_hourly_count (malfunction_type, count, hour_timestamp)
            VALUES (%s, GREATEST(0, %s), %s)
            ON DUPLICATE KEY UPDATE 
                count = GREATEST(0, count + %s),
                updated_at = CURRENT_TIMESTAMP
            """
            cursor.execute(hourly_sql, (status, count_change, target_hour, count_change))
            
        except Exception as e:
            logging.warning(f"更新统计表失败: {e}")
    
    def insert_batch_malfunction_data(self, batch_size=20):
        """批量插入不同时间段的故障数据"""
        try:
            if not self.connection or not self.connection.is_connected():
                if not self.connect_db():
                    return False
            
            cursor = self.connection.cursor()
            now = datetime.now()
            
            # 按时间段插入数据，模拟真实的故障分布
            time_periods = [
                {'hours_ago': 1, 'count': 8, 'types': ['A', 'B', 'C']},      # 最近1小时，主要是A、B、C类故障
                {'hours_ago': 3, 'count': 12, 'types': ['B', 'D', 'E']},     # 3小时前，主要是B、D、E类故障
                {'hours_ago': 6, 'count': 15, 'types': ['A', 'C', 'F', 'G']}, # 6小时前，多种故障类型
                {'hours_ago': 12, 'count': 10, 'types': ['H', 'I', 'J']},    # 12小时前，其他故障类型
                {'hours_ago': 24, 'count': 5, 'types': ['A', 'E']}           # 24小时前，少量故障
            ]
            
            total_inserted = 0
            
            for period in time_periods:
                base_time = now - timedelta(hours=period['hours_ago'])
                
                for i in range(period['count']):
                    # 在该时间段内随机分布
                    random_minutes = random.randint(-30, 30)  # 前后30分钟内随机
                    timestamp = base_time + timedelta(minutes=random_minutes)
                    
                    # 从该时间段的故障类型中随机选择
                    malfunction_type = random.choice(period['types'])
                    line = random.randint(1, 5)
                    
                    # 大部分是故障发生（cmd=1），少部分是故障恢复（cmd=0）
                    cmd = 1 if random.random() < 0.8 else 0
                    
                    # 插入原始数据
                    insert_sql = """
                    INSERT INTO malfunction_raw_data (line, status, cmd, timestamp) 
                    VALUES (%s, %s, %s, %s)
                    """
                    cursor.execute(insert_sql, (line, malfunction_type, cmd, timestamp))
                    
                    # 更新统计表
                    self._update_statistics_with_time(cursor, malfunction_type, cmd, timestamp)
                    
                    total_inserted += 1
            
            cursor.close()
            logging.info(f"批量插入完成，总共插入 {total_inserted} 条故障数据")
            return True
            
        except Error as e:
            logging.error(f"批量插入故障数据失败: {e}")
            return False
    
    def clear_test_data(self):
        """清除测试数据"""
        try:
            if not self.connection or not self.connection.is_connected():
                if not self.connect_db():
                    return False
            
            cursor = self.connection.cursor()
            
            # 清除故障相关数据
            cursor.execute("DELETE FROM malfunction_raw_data WHERE timestamp >= CURDATE() - INTERVAL 2 DAY")
            cursor.execute("DELETE FROM malfunction_daily_count WHERE date_timestamp >= CURDATE() - INTERVAL 2 DAY")
            cursor.execute("DELETE FROM malfunction_hourly_count WHERE hour_timestamp >= CURDATE() - INTERVAL 2 DAY")
            
            # 清除生产线数据（最近2天的）
            cursor.execute("DELETE FROM line1_message_log WHERE received_at >= CURDATE() - INTERVAL 2 DAY")
            
            cursor.close()
            logging.info("测试数据清除完成")
            return True
            
        except Error as e:
            logging.error(f"清除测试数据失败: {e}")
            return False
    
    def generate_realistic_data(self):
        """生成更真实的测试数据"""
        try:
            logging.info("开始生成真实测试数据...")
            
            # 1. 插入生产线数据（模拟正常生产）
            self.insert_production_data(200)
            
            # 2. 批量插入故障数据（按时间段分布）
            self.insert_batch_malfunction_data()
            
            # 3. 额外插入一些随机故障数据
            self.insert_malfunction_data(30)
            
            logging.info("真实测试数据生成完成！")
            return True
            
        except Exception as e:
            logging.error(f"生成测试数据失败: {e}")
            return False

def main():
    """主函数"""
    inserter = TestDataInserter()
    
    try:
        if not inserter.connect_db():
            print("数据库连接失败，请检查配置")
            return
        
        print("=== 测试数据插入工具 ===")
        print("1. 插入生产线数据")
        print("2. 插入故障数据")
        print("3. 生成真实测试数据（推荐）")
        print("4. 清除测试数据")
        print("5. 退出")
        
        while True:
            choice = input("\n请选择操作 (1-5): ").strip()
            
            if choice == '1':
                count = int(input("请输入要插入的生产线数据条数 (默认100): ") or "100")
                if inserter.insert_production_data(count):
                    print(f"成功插入 {count} 条生产线数据")
                else:
                    print("插入生产线数据失败")
                    
            elif choice == '2':
                count = int(input("请输入要插入的故障数据条数 (默认50): ") or "50")
                if inserter.insert_malfunction_data(count):
                    print(f"成功插入 {count} 条故障数据")
                else:
                    print("插入故障数据失败")
                    
            elif choice == '3':
                if inserter.generate_realistic_data():
                    print("真实测试数据生成完成！")
                    print("数据包括：")
                    print("- 200条生产线数据（过去24小时）")
                    print("- 50条按时间段分布的故障数据")
                    print("- 30条随机故障数据")
                else:
                    print("生成测试数据失败")
                    
            elif choice == '4':
                confirm = input("确定要清除测试数据吗？(y/N): ").strip().lower()
                if confirm == 'y':
                    if inserter.clear_test_data():
                        print("测试数据清除完成")
                    else:
                        print("清除测试数据失败")
                else:
                    print("操作已取消")
                    
            elif choice == '5':
                print("退出程序")
                break
                
            else:
                print("无效选择，请重新输入")
    
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {e}")
    finally:
        inserter.close_db()

if __name__ == '__main__':
    main()