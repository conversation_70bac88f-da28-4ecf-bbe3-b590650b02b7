-- 创建生产线1的计数表
CREATE TABLE line1_production_count (
    id INT AUTO_INCREMENT PRIMARY KEY,
    total_count INT NOT NULL DEFAULT 0,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建生产线1的每小时计数表
CREATE TABLE line1_hourly_count (
    id INT AUTO_INCREMENT PRIMARY KEY,
    hour_timestamp DATETIME NOT NULL,
    count INT NOT NULL DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建生产线1的每日计数表
CREATE TABLE line1_daily_count (
    id INT AUTO_INCREMENT PRIMARY KEY,
    date_timestamp DATE NOT NULL,
    count INT NOT NULL DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建原始消息记录表
CREATE TABLE line1_message_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    topic VARCHAR(100) NOT NULL,
    qos INT DEFAULT 0,
    counter INT,
    received_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 初始化总计数器
INSERT INTO line1_production_count (total_count) VALUES (0);

-- 创建存储过程，用于处理新的计数器消息
DELIMITER //
CREATE PROCEDURE process_line1_counter(IN msg_topic VARCHAR(100), IN msg_time TIMESTAMP)
BEGIN
    DECLARE current_hour DATETIME;
    DECLARE current_day DATE;
    DECLARE hour_count INT;
    DECLARE day_count INT;
    
    -- 只处理line1/counter消息
    IF msg_topic = 'factory/line1/counter' THEN
        -- 更新总计数
        UPDATE line1_production_count SET 
            total_count = total_count + 1,
            last_updated = msg_time;
        
        -- 获取当前小时和日期
        SET current_hour = DATE_FORMAT(msg_time, '%Y-%m-%d %H:00:00');
        SET current_day = DATE(msg_time);
        
        -- 检查小时记录是否存在
        SELECT COUNT(*) INTO hour_count FROM line1_hourly_count 
        WHERE hour_timestamp = current_hour;
        
        -- 更新或插入小时计数
        IF hour_count > 0 THEN
            UPDATE line1_hourly_count 
            SET count = count + 1 
            WHERE hour_timestamp = current_hour;
        ELSE
            INSERT INTO line1_hourly_count (hour_timestamp, count)
            VALUES (current_hour, 1);
        END IF;
        
        -- 检查日记录是否存在
        SELECT COUNT(*) INTO day_count FROM line1_daily_count 
        WHERE date_timestamp = current_day;
        
        -- 更新或插入日计数
        IF day_count > 0 THEN
            UPDATE line1_daily_count 
            SET count = count + 1 
            WHERE date_timestamp = current_day;
        ELSE
            INSERT INTO line1_daily_count (date_timestamp, count)
            VALUES (current_day, 1);
        END IF;
    END IF;
END //
DELIMITER ;

-- 创建触发器，在收到新消息时增加计数
DELIMITER //
CREATE TRIGGER trigger_line1_counter
AFTER INSERT ON line1_message_log
FOR EACH ROW
BEGIN
    IF NEW.topic = 'factory/line1/counter' THEN
        CALL process_line1_counter(NEW.topic, NEW.received_at);
    END IF;
END //
DELIMITER ;

-- 插入示例数据
INSERT INTO line1_message_log (topic, qos, counter, received_at)
VALUES ('factory/line1/counter', 0, 34, '2025-08-16 15:48:24.622');

-- 创建索引以提高查询性能
CREATE INDEX idx_line1_message_topic ON line1_message_log(topic);
CREATE INDEX idx_line1_message_timestamp ON line1_message_log(received_at);
CREATE INDEX idx_line1_hourly_timestamp ON line1_hourly_count(hour_timestamp);
CREATE INDEX idx_line1_daily_timestamp ON line1_daily_count(date_timestamp);

-- 创建视图用于查询当前计数
CREATE VIEW view_line1_current_count AS
SELECT total_count, last_updated FROM line1_production_count;

-- 创建视图用于查询今天的每小时计数
CREATE VIEW view_line1_today_hourly AS
SELECT 
    hour_timestamp,
    count,
    HOUR(hour_timestamp) AS hour
FROM line1_hourly_count
WHERE DATE(hour_timestamp) = CURDATE()
ORDER BY hour_timestamp;

-- 创建视图用于查询最近7天的每日计数
CREATE VIEW view_line1_recent_daily AS
SELECT 
    date_timestamp,
    count
FROM line1_daily_count
WHERE date_timestamp >= DATE_SUB(CURDATE(), INTERVAL 6 DAY)
ORDER BY date_timestamp;
