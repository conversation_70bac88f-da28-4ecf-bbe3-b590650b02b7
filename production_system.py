import pymysql
import mysql.connector
from mysql.connector import Error
import time
import json
import threading
import logging
from datetime import datetime
from flask import Flask, request, jsonify, send_file
import paho.mqtt.client as mqtt
from flask_cors import CORS

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('production_system.log'),
        logging.StreamHandler()
    ]
)

# Flask应用
app = Flask(__name__)
CORS(app)  # 启用CORS，允许所有域的跨域请求

# 数据库配置 - PyMySQL (用于生产线数据)
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '9f81cc9ba8f6458d',  # 请修改为您的数据库密码
    'db': 'production_data',   # 请修改为您的数据库名
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

# 数据库配置 - MySQL Connector (用于故障数据)
MALFUNCTION_DB_CONFIG = {
    'host': 'localhost',
    'database': 'production_data',
    'user': 'root',
    'password': '9f81cc9ba8f6458d',  # 请修改为实际密码
    'charset': 'utf8mb4',
    'autocommit': True
}

class MalfunctionAPI:
    def __init__(self):
        self.connection = None
        
    def connect_db(self):
        """连接数据库"""
        try:
            self.connection = mysql.connector.connect(**MALFUNCTION_DB_CONFIG)
            if self.connection.is_connected():
                logging.info("故障数据库连接成功")
                return True
        except Error as e:
            logging.error(f"故障数据库连接失败: {e}")
            return False
        return False
    
    def close_db(self):
        """关闭数据库连接"""
        if self.connection and self.connection.is_connected():
            self.connection.close()
            logging.info("故障数据库连接已关闭")
    
    def validate_data(self, data):
        """验证数据格式"""
        required_fields = ['line', 'status', 'cmd']
        
        # 检查必需字段
        for field in required_fields:
            if field not in data:
                return False, f"缺少必需字段: {field}"
        
        # 验证数据类型和范围
        try:
            line = int(data['line'])
            if line <= 0:
                return False, "生产线编号必须大于0"
        except (ValueError, TypeError):
            return False, "生产线编号必须是整数"
        
        # 验证状态类型
        status = str(data['status']).upper()
        if status not in ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J']:
            return False, "状态类型必须是A-J之间的字母"
        
        # 验证命令
        try:
            cmd = int(data['cmd'])
            if cmd not in [0, 1]:
                return False, "命令必须是0或1"
        except (ValueError, TypeError):
            return False, "命令必须是整数"
        
        return True, "数据验证通过"
    
    def insert_malfunction_data(self, line, status, cmd):
        """插入坏机数据"""
        try:
            if not self.connection or not self.connection.is_connected():
                if not self.connect_db():
                    return False, "数据库连接失败"
            
            cursor = self.connection.cursor()
            
            # 先尝试调用存储过程，如果不存在则直接插入
            try:
                cursor.callproc('insert_malfunction_raw_data', [line, status, cmd])
                cursor.execute("SELECT LAST_INSERT_ID()")
                record_id = cursor.fetchone()[0]
            except Error as proc_error:
                if "does not exist" in str(proc_error):
                    # 存储过程不存在，直接插入数据
                    logging.warning("存储过程不存在，使用直接插入方式")
                    
                    # 插入原始数据
                    insert_raw_sql = """
                    INSERT INTO malfunction_raw_data (line, status, cmd) 
                    VALUES (%s, %s, %s)
                    """
                    cursor.execute(insert_raw_sql, (line, status, cmd))
                    record_id = cursor.lastrowid
                    
                    # 更新统计表
                    self._update_statistics(cursor, status, cmd)
                else:
                    raise proc_error
            
            cursor.close()
            
            logging.info(f"成功插入坏机数据: line={line}, status={status}, cmd={cmd}, id={record_id}")
            return True, f"数据插入成功，记录ID: {record_id}"
            
        except Error as e:
            logging.error(f"插入数据失败: {e}")
            return False, f"数据库错误: {str(e)}"
        except Exception as e:
            logging.error(f"未知错误: {e}")
            return False, f"未知错误: {str(e)}"
    
    def _update_statistics(self, cursor, status, cmd):
        """更新统计表"""
        try:
            target_date = datetime.now().date()
            target_hour = datetime.now().replace(minute=0, second=0, microsecond=0)
            count_change = 1 if cmd == 1 else -1
            
            # 更新每日计数
            daily_sql = """
            INSERT INTO malfunction_daily_count (malfunction_type, count, date_timestamp)
            VALUES (%s, GREATEST(0, %s), %s)
            ON DUPLICATE KEY UPDATE 
                count = GREATEST(0, count + %s),
                updated_at = CURRENT_TIMESTAMP
            """
            cursor.execute(daily_sql, (status, count_change, target_date, count_change))
            
            # 更新每小时计数
            hourly_sql = """
            INSERT INTO malfunction_hourly_count (malfunction_type, count, hour_timestamp)
            VALUES (%s, GREATEST(0, %s), %s)
            ON DUPLICATE KEY UPDATE 
                count = GREATEST(0, count + %s),
                updated_at = CURRENT_TIMESTAMP
            """
            cursor.execute(hourly_sql, (status, count_change, target_hour, count_change))
            
        except Exception as e:
            logging.warning(f"更新统计表失败: {e}")
    
    def get_today_stats(self):
        """获取今日统计数据"""
        try:
            if not self.connection or not self.connection.is_connected():
                if not self.connect_db():
                    return None, "数据库连接失败"
            
            cursor = self.connection.cursor(dictionary=True)
            
            # 尝试调用存储过程，如果失败则使用直接查询
            try:
                cursor.callproc('get_today_malfunction_stats')
                results = []
                for result in cursor.stored_results():
                    results.extend(result.fetchall())
            except Error as proc_error:
                if "does not exist" in str(proc_error):
                    # 存储过程不存在，使用直接查询，关联malfunction_type_info表
                    logging.warning("存储过程不存在，使用直接查询")
                    query = """
                    SELECT 
                        mdc.malfunction_type,
                        COALESCE(mti.type_name, 
                            CASE mdc.malfunction_type
                                WHEN 'A' THEN '机械故障'
                                WHEN 'B' THEN '电气故障'
                                WHEN 'C' THEN '传感器故障'
                                WHEN 'D' THEN '软件故障'
                                WHEN 'E' THEN '温度异常'
                                WHEN 'F' THEN '压力异常'
                                WHEN 'G' THEN '材料卡顿'
                                WHEN 'H' THEN '清洁问题'
                                WHEN 'I' THEN '校准偏差'
                                WHEN 'J' THEN '其他故障'
                                ELSE '未知故障'
                            END
                        ) as type_name,
                        mdc.count
                    FROM malfunction_daily_count mdc
                    LEFT JOIN malfunction_type_info mti ON mdc.malfunction_type = mti.type_code
                    WHERE mdc.date_timestamp = CURDATE()
                    ORDER BY mdc.malfunction_type
                    """
                    cursor.execute(query)
                    results = cursor.fetchall()
                else:
                    raise proc_error
            
            cursor.close()
            return results, "获取统计数据成功"
            
        except Error as e:
            logging.error(f"获取统计数据失败: {e}")
            return None, f"数据库错误: {str(e)}"
    
    def get_raw_data_by_line(self, line):
        """获取指定生产线的原始数据"""
        try:
            if not self.connection or not self.connection.is_connected():
                if not self.connect_db():
                    return None, "数据库连接失败"
            
            cursor = self.connection.cursor(dictionary=True)
            cursor.callproc('get_raw_data_by_line', [line])
            
            results = []
            for result in cursor.stored_results():
                results.extend(result.fetchall())
            
            cursor.close()
            return results, "获取原始数据成功"
            
        except Error as e:
            logging.error(f"获取原始数据失败: {e}")
            return None, f"数据库错误: {str(e)}"
    
    def get_hourly_stats(self, date=None):
        """获取每小时统计数据"""
        try:
            if not self.connection or not self.connection.is_connected():
                if not self.connect_db():
                    return None, "数据库连接失败"
            
            cursor = self.connection.cursor(dictionary=True)
            
            # 如果没有指定日期，使用今天
            if date is None:
                date = datetime.now().date()
            
            # 查询指定日期的每小时统计数据，关联malfunction_type_info表获取类型名称
            query = """
            SELECT 
                mhc.malfunction_type,
                COALESCE(mti.type_name, 
                    CASE mhc.malfunction_type
                        WHEN 'A' THEN '机械故障'
                        WHEN 'B' THEN '电气故障'
                        WHEN 'C' THEN '传感器故障'
                        WHEN 'D' THEN '软件故障'
                        WHEN 'E' THEN '温度异常'
                        WHEN 'F' THEN '压力异常'
                        WHEN 'G' THEN '材料卡顿'
                        WHEN 'H' THEN '清洁问题'
                        WHEN 'I' THEN '校准偏差'
                        WHEN 'J' THEN '其他故障'
                        ELSE '未知故障'
                    END
                ) as type_name,
                mhc.count,
                mhc.hour_timestamp,
                HOUR(mhc.hour_timestamp) as hour,
                mhc.updated_at
            FROM malfunction_hourly_count mhc
            LEFT JOIN malfunction_type_info mti ON mhc.malfunction_type = mti.type_code
            WHERE DATE(mhc.hour_timestamp) = %s
            ORDER BY mhc.hour_timestamp, mhc.malfunction_type
            """
            
            cursor.execute(query, (date,))
            results = cursor.fetchall()
            
            cursor.close()
            return results, "获取每小时统计数据成功"
            
        except Error as e:
            logging.error(f"获取每小时统计数据失败: {e}")
            return None, f"数据库错误: {str(e)}"
    
    def get_hourly_stats_by_type(self, malfunction_type, date=None):
        """获取指定故障类型的每小时统计数据"""
        try:
            if not self.connection or not self.connection.is_connected():
                if not self.connect_db():
                    return None, "数据库连接失败"
            
            cursor = self.connection.cursor(dictionary=True)
            
            # 如果没有指定日期，使用今天
            if date is None:
                date = datetime.now().date()
            
            # 查询指定故障类型和日期的每小时统计数据，关联malfunction_type_info表获取类型名称
            query = """
            SELECT 
                mhc.malfunction_type,
                COALESCE(mti.type_name, 
                    CASE mhc.malfunction_type
                        WHEN 'A' THEN '机械故障'
                        WHEN 'B' THEN '电气故障'
                        WHEN 'C' THEN '传感器故障'
                        WHEN 'D' THEN '软件故障'
                        WHEN 'E' THEN '温度异常'
                        WHEN 'F' THEN '压力异常'
                        WHEN 'G' THEN '材料卡顿'
                        WHEN 'H' THEN '清洁问题'
                        WHEN 'I' THEN '校准偏差'
                        WHEN 'J' THEN '其他故障'
                        ELSE '未知故障'
                    END
                ) as type_name,
                mhc.count,
                mhc.hour_timestamp,
                HOUR(mhc.hour_timestamp) as hour,
                mhc.updated_at
            FROM malfunction_hourly_count mhc
            LEFT JOIN malfunction_type_info mti ON mhc.malfunction_type = mti.type_code
            WHERE DATE(mhc.hour_timestamp) = %s AND mhc.malfunction_type = %s
            ORDER BY mhc.hour_timestamp
            """
            
            cursor.execute(query, (date, malfunction_type.upper()))
            results = cursor.fetchall()
            
            cursor.close()
            return results, f"获取故障类型 {malfunction_type} 的每小时统计数据成功"
            
        except Error as e:
            logging.error(f"获取每小时统计数据失败: {e}")
            return None, f"数据库错误: {str(e)}"

# 创建故障API实例
malfunction_api = MalfunctionAPI()

# MQTT配置
MQTT_BROKER = "8.134.113.109"  # 请修改为您的MQTT代理地址
MQTT_PORT = 1883
MQTT_TOPIC = "factory/line1/counter"
MQTT_CLIENT_ID = "production_counter_subscriber"

def get_db_connection():
    """创建数据库连接"""
    return pymysql.connect(**DB_CONFIG)

# ===== API部分 =====

@app.route('/api/counter', methods=['POST'])
def receive_counter():
    """接收生产线计数器数据"""
    data = request.json
    
    if not data or 'topic' not in data:
        return jsonify({'error': '无效的数据格式'}), 400
    
    topic = data.get('topic')
    qos = data.get('qos', 0)
    counter = data.get('counter')
    
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # 插入消息日志
            sql = """
            INSERT INTO line1_message_log (topic, qos, counter, received_at)
            VALUES (%s, %s, %s, %s)
            """
            cursor.execute(sql, (topic, qos, counter, datetime.now()))
        conn.commit()
        conn.close()
        return jsonify({'status': 'success', 'message': '数据已接收'}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/stats/current', methods=['GET'])
def get_current_count():
    """获取当前总计数"""
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            cursor.execute("SELECT * FROM view_line1_current_count")
            result = cursor.fetchone()
        conn.close()
        return jsonify(result), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/stats/hourly', methods=['GET'])
def get_hourly_stats():
    """获取今日每小时统计"""
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            cursor.execute("SELECT * FROM view_line1_today_hourly")
            result = cursor.fetchall()
        conn.close()
        return jsonify(result), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/stats/hourly/recent', methods=['GET'])
def get_recent_hourly_stats():
    """获取最新10个小时统计"""
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            cursor.execute("""
                SELECT * FROM line1_hourly_count 
                ORDER BY hour_timestamp DESC 
                LIMIT 10
            """)
            result = cursor.fetchall()
            # 将结果转换为可序列化的格式
            for item in result:
                if 'hour_timestamp' in item:
                    item['hour_timestamp'] = item['hour_timestamp'].strftime('%Y-%m-%d %H:%M:%S')
                if 'created_at' in item:
                    item['created_at'] = item['created_at'].strftime('%Y-%m-%d %H:%M:%S')
        conn.close()
        return jsonify(result), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/stats/daily', methods=['GET'])
def get_daily_stats():
    """获取最近7天统计"""
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            cursor.execute("SELECT * FROM view_line1_recent_daily")
            result = cursor.fetchall()
        conn.close()
        return jsonify(result), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/stats/daily/custom', methods=['GET'])
def get_custom_daily_stats():
    """获取指定天数的每日统计"""
    try:
        days = request.args.get('days', default=7, type=int)
        if days <= 0 or days > 90:  # 限制查询范围
            days = 7
            
        conn = get_db_connection()
        with conn.cursor() as cursor:
            cursor.execute("""
                SELECT * FROM line1_daily_count 
                ORDER BY date_timestamp DESC 
                LIMIT %s
            """, (days,))
            result = cursor.fetchall()
            # 将结果转换为可序列化的格式
            for item in result:
                if 'date_timestamp' in item:
                    item['date_timestamp'] = item['date_timestamp'].strftime('%Y-%m-%d')
                if 'created_at' in item:
                    item['created_at'] = item['created_at'].strftime('%Y-%m-%d %H:%M:%S')
        conn.close()
        return jsonify(result), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/chart.js')
def serve_chart_js():
    """提供chart.js文件"""
    return send_file('chart.js', mimetype='application/javascript')

@app.route('/api/simulate', methods=['POST'])
def simulate_counter():
    """模拟生产线计数器数据（用于测试）"""
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # 插入模拟消息
            sql = """
            INSERT INTO line1_message_log (topic, qos, counter, received_at)
            VALUES ('factory/line1/counter', 0, %s, %s)
            """
            counter = int(time.time()) % 100  # 模拟计数器值
            cursor.execute(sql, (counter, datetime.now()))
        conn.commit()
        conn.close()
        return jsonify({'status': 'success', 'message': '已发送模拟数据', 'counter': counter}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# ===== 故障数据API部分 =====

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({
        'status': 'ok',
        'timestamp': datetime.now().isoformat(),
        'service': 'production_system'
    })

@app.route('/malfunction', methods=['POST'])
def receive_malfunction_data():
    """接收坏机数据"""
    try:
        # 获取JSON数据
        if not request.is_json:
            return jsonify({
                'success': False,
                'message': '请求必须是JSON格式',
                'timestamp': datetime.now().isoformat()
            }), 400
        
        data = request.get_json()
        logging.info(f"接收到坏机数据: {data}")
        
        # 验证数据
        is_valid, message = malfunction_api.validate_data(data)
        if not is_valid:
            return jsonify({
                'success': False,
                'message': message,
                'timestamp': datetime.now().isoformat()
            }), 400
        
        # 插入数据
        success, message = malfunction_api.insert_malfunction_data(
            int(data['line']),
            str(data['status']).upper(),
            int(data['cmd'])
        )
        
        if success:
            return jsonify({
                'success': True,
                'message': message,
                'data': data,
                'timestamp': datetime.now().isoformat()
            }), 200
        else:
            return jsonify({
                'success': False,
                'message': message,
                'timestamp': datetime.now().isoformat()
            }), 500
            
    except Exception as e:
        logging.error(f"处理请求时发生错误: {e}")
        return jsonify({
            'success': False,
            'message': f'服务器内部错误: {str(e)}',
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/malfunction/batch', methods=['POST'])
def receive_batch_malfunction_data():
    """批量接收坏机数据"""
    try:
        if not request.is_json:
            return jsonify({
                'success': False,
                'message': '请求必须是JSON格式',
                'timestamp': datetime.now().isoformat()
            }), 400
        
        data_list = request.get_json()
        
        if not isinstance(data_list, list):
            return jsonify({
                'success': False,
                'message': '批量数据必须是数组格式',
                'timestamp': datetime.now().isoformat()
            }), 400
        
        results = []
        success_count = 0
        
        for i, data in enumerate(data_list):
            # 验证数据
            is_valid, message = malfunction_api.validate_data(data)
            if not is_valid:
                results.append({
                    'index': i,
                    'success': False,
                    'message': message,
                    'data': data
                })
                continue
            
            # 插入数据
            success, message = malfunction_api.insert_malfunction_data(
                int(data['line']),
                str(data['status']).upper(),
                int(data['cmd'])
            )
            
            results.append({
                'index': i,
                'success': success,
                'message': message,
                'data': data
            })
            
            if success:
                success_count += 1
        
        return jsonify({
            'success': True,
            'message': f'批量处理完成，成功: {success_count}/{len(data_list)}',
            'results': results,
            'timestamp': datetime.now().isoformat()
        }), 200
        
    except Exception as e:
        logging.error(f"批量处理请求时发生错误: {e}")
        return jsonify({
            'success': False,
            'message': f'服务器内部错误: {str(e)}',
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/malfunction/stats', methods=['GET'])
def get_malfunction_stats():
    """获取今日坏机统计"""
    try:
        results, message = malfunction_api.get_today_stats()
        
        if results is not None:
            return jsonify({
                'success': True,
                'message': message,
                'data': results,
                'timestamp': datetime.now().isoformat()
            }), 200
        else:
            return jsonify({
                'success': False,
                'message': message,
                'timestamp': datetime.now().isoformat()
            }), 500
            
    except Exception as e:
        logging.error(f"获取统计数据时发生错误: {e}")
        return jsonify({
            'success': False,
            'message': f'服务器内部错误: {str(e)}',
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/malfunction/raw/<int:line>', methods=['GET'])
def get_raw_data_by_line(line):
    """获取指定生产线的原始数据"""
    try:
        results, message = malfunction_api.get_raw_data_by_line(line)
        
        if results is not None:
            return jsonify({
                'success': True,
                'message': message,
                'data': results,
                'line': line,
                'timestamp': datetime.now().isoformat()
            }), 200
        else:
            return jsonify({
                'success': False,
                'message': message,
                'timestamp': datetime.now().isoformat()
            }), 500
            
    except Exception as e:
        logging.error(f"获取原始数据时发生错误: {e}")
        return jsonify({
            'success': False,
            'message': f'服务器内部错误: {str(e)}',
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/malfunction/hourly', methods=['GET'])
def get_hourly_malfunction_stats():
    """获取每小时坏机统计数据"""
    try:
        # 获取查询参数
        date_str = request.args.get('date')  # 格式: YYYY-MM-DD
        malfunction_type = request.args.get('type')  # 故障类型 A-J
        
        target_date = None
        if date_str:
            try:
                target_date = datetime.strptime(date_str, '%Y-%m-%d').date()
            except ValueError:
                return jsonify({
                    'success': False,
                    'message': '日期格式错误，请使用 YYYY-MM-DD 格式',
                    'timestamp': datetime.now().isoformat()
                }), 400
        
        # 根据是否指定故障类型选择不同的查询方法
        if malfunction_type:
            # 验证故障类型
            if malfunction_type.upper() not in ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J']:
                return jsonify({
                    'success': False,
                    'message': '故障类型必须是A-J之间的字母',
                    'timestamp': datetime.now().isoformat()
                }), 400
            
            results, message = malfunction_api.get_hourly_stats_by_type(malfunction_type, target_date)
        else:
            results, message = malfunction_api.get_hourly_stats(target_date)
        
        if results is not None:
            # 组织数据为更友好的格式
            organized_data = {}
            for row in results:
                hour = row['hour']
                malfunction_type_key = row['malfunction_type']
                
                if hour not in organized_data:
                    organized_data[hour] = {}
                
                organized_data[hour][malfunction_type_key] = {
                    'count': row['count'],
                    'hour_timestamp': row['hour_timestamp'].isoformat() if row['hour_timestamp'] else None,
                    'updated_at': row['updated_at'].isoformat() if row['updated_at'] else None
                }
            
            return jsonify({
                'success': True,
                'message': message,
                'data': {
                    'raw_data': results,
                    'organized_by_hour': organized_data,
                    'query_date': target_date.isoformat() if target_date else datetime.now().date().isoformat(),
                    'malfunction_type': malfunction_type
                },
                'timestamp': datetime.now().isoformat()
            }), 200
        else:
            return jsonify({
                'success': False,
                'message': message,
                'timestamp': datetime.now().isoformat()
            }), 500
            
    except Exception as e:
        logging.error(f"获取每小时统计数据时发生错误: {e}")
        return jsonify({
            'success': False,
            'message': f'服务器内部错误: {str(e)}',
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/malfunction/hourly/summary', methods=['GET'])
def get_hourly_summary():
    """获取每小时统计数据汇总"""
    try:
        date_str = request.args.get('date')  # 格式: YYYY-MM-DD
        
        target_date = None
        if date_str:
            try:
                target_date = datetime.strptime(date_str, '%Y-%m-%d').date()
            except ValueError:
                return jsonify({
                    'success': False,
                    'message': '日期格式错误，请使用 YYYY-MM-DD 格式'
                }), 400
        
        results, message = malfunction_api.get_hourly_stats(target_date)
        
        if results is not None:
            # 创建简化的汇总数据
            hourly_summary = {}
            
            for row in results:
                hour = row['hour']
                malfunction_type_key = row['malfunction_type']
                type_name = row.get('type_name', f'故障类型{malfunction_type_key}')
                count = row['count']
                
                # 按小时汇总
                if hour not in hourly_summary:
                    hourly_summary[hour] = {'total': 0, 'types': {}}
                
                hourly_summary[hour]['total'] += count
                hourly_summary[hour]['types'][malfunction_type_key] = {
                    'name': type_name,
                    'count': count
                }
            
            return jsonify({
                'success': True,
                'message': message,
                'data': hourly_summary
            }), 200
        else:
            return jsonify({
                'success': False,
                'message': message
            }), 500
            
    except Exception as e:
        logging.error(f"获取每小时汇总数据时发生错误: {e}")
        return jsonify({
            'success': False,
            'message': f'服务器内部错误: {str(e)}'
        }), 500

@app.errorhandler(404)
def not_found(error):
    return jsonify({
        'success': False,
        'message': '接口不存在',
        'timestamp': datetime.now().isoformat()
    }), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({
        'success': False,
        'message': '服务器内部错误',
        'timestamp': datetime.now().isoformat()
    }), 500

# ===== MQTT部分 =====

def on_connect(client, userdata, flags, rc):
    """连接MQTT代理后的回调"""
    print(f"已连接到MQTT代理，结果代码：{rc}")
    client.subscribe(MQTT_TOPIC)
    print(f"已订阅主题：{MQTT_TOPIC}")

def on_message(client, userdata, msg):
    """接收到消息后的回调"""
    try:
        print(f"收到消息：{msg.topic} {msg.payload}")
        
        # 解析消息
        payload = msg.payload.decode('utf-8')
        data = json.loads(payload)
        
        # 获取计数器值
        counter = data.get('counter')
        
        # 存储到数据库
        conn = get_db_connection()
        with conn.cursor() as cursor:
            sql = """
            INSERT INTO line1_message_log (topic, qos, counter, received_at)
            VALUES (%s, %s, %s, %s)
            """
            cursor.execute(sql, (msg.topic, 0, counter, datetime.now()))
        conn.commit()
        conn.close()
        
        print(f"已存储计数器值：{counter}")
    except Exception as e:
        print(f"处理消息时出错：{e}")

def start_mqtt_client():
    """启动MQTT客户端"""
    client = mqtt.Client(client_id=MQTT_CLIENT_ID)
    client.on_connect = on_connect
    client.on_message = on_message
    
    try:
        client.connect(MQTT_BROKER, MQTT_PORT, 60)
        print(f"正在连接到MQTT代理：{MQTT_BROKER}:{MQTT_PORT}")
        
        # 循环处理网络流量和回调
        client.loop_forever()
    except Exception as e:
        print(f"连接到MQTT代理失败：{e}")

# ===== 主函数 =====

def main():
    """主函数"""
    # 启动时连接故障数据库
    if malfunction_api.connect_db():
        print("故障数据库连接成功")
    else:
        print("故障数据库连接失败，故障相关功能可能无法正常使用")
    
    # 启动MQTT客户端（在单独的线程中）
    mqtt_thread = threading.Thread(target=start_mqtt_client)
    mqtt_thread.daemon = True  # 设置为守护线程，这样主程序退出时线程也会退出
    mqtt_thread.start()
    
    # 启动Flask应用
    print("启动生产系统API服务...")
    print("生产线计数器接口:")
    print("  POST /api/counter - 接收生产线计数器数据")
    print("  GET  /api/stats/current - 获取当前总计数")
    print("  GET  /api/stats/hourly - 获取今日每小时统计")
    print("  GET  /api/stats/hourly/recent - 获取最新10个小时统计")
    print("  GET  /api/stats/daily - 获取最近7天统计")
    print("  GET  /api/stats/daily/custom - 获取指定天数的每日统计")
    print("  POST /api/simulate - 模拟生产线计数器数据")
    print("")
    print("故障数据接口:")
    print("  POST /malfunction - 接收单条坏机数据")
    print("  POST /malfunction/batch - 批量接收坏机数据")
    print("  GET  /malfunction/stats - 获取今日故障统计")
    print("  GET  /malfunction/raw/<line> - 获取指定生产线原始数据")
    print("  GET  /malfunction/hourly - 获取每小时故障统计数据")
    print("  GET  /malfunction/hourly/summary - 获取每小时故障汇总数据")
    print("  GET  /health - 健康检查")
    
    app.run(debug=True, host='0.0.0.0', port=5000, use_reloader=False)

if __name__ == '__main__':
    main()
